package za.co.wethinkcode.robots.commands;

import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.World;

import static org.junit.jupiter.api.Assertions.*;

public class WorldCommandTest {

    @Test
    public void testWorldCommand() {
        String clientId = "client-xyz";
        Robot robot = new Robot("TestBot", "tank");
        World world = new World(10, 10);
        WorldCommand command = new WorldCommand(robot, new String[]{});

        world.execute(command, clientId, response -> {
            assertTrue(response.isOKResponse());
            assertNotNull(response.object.getJSONObject("data"));
            assertNotNull(response.object.getJSONObject("data").getJSONArray("dimensions"));
            assertEquals(10, response.object.getJSONObject("data").getJSONArray("dimensions").getInt(0));
            assertEquals(10, response.object.getJSONObject("data").getJSONArray("dimensions").getInt(1));
        });
    }
}
