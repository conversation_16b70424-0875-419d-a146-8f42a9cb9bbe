package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;

/**
 * Command to get world configuration parameters.
 * Returns information about world dimensions, visibility, reload time, repair time, etc.
 */
public class WorldCommand extends Command {

    public WorldCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "world";
    }
}
