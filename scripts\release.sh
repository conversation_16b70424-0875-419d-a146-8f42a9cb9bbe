#!/bin/bash

set -e  # Stop on error

echo "🔧 Preparing release..."

cp pom.xml pom.xml.bak

sed -i '0,/\(<version>.*\)-SNAPSHOT\(.*<\/version>\)/s//\1\2/' pom.xml

VERSION=$(grep '<version>' pom.xml | head -1 | sed -E 's/.*<version>([^<]+)<\/version>.*/\1/')
echo "📦 Building release version $VERSION..."

mvn clean package

echo "✅ Release build complete: target/${PROJECT_NAME}-${VERSION}-jar-with-dependencies.jar"
