package za.co.wethinkcode.robots.commands;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.Robot;

import java.util.Arrays;
import java.util.Set;

/**
 * Abstract representation of a command sent to robots.
 * Defines interface and common behavior for all commands.
 */
public abstract class Command {
    public Robot robot;
    public String[] arguments;

    public Command(Robot robot, String[] arguments) {
        this.robot = robot;
        this.arguments = arguments;
    }
    // made a set to replaces long switch-case for clarity
    private static final Set<String>VALID_COMMANDS = Set.of(
            "forward", "back", "turn", "look", "state", "launch", "dump", "orientation", "shutdown",
            "disconnect", "fire", "repair", "reload", "help", "world", "mine"
    );

    public static boolean isValidCommand(String command) {
        return VALID_COMMANDS.contains(command.toLowerCase());

    }

    public String toJSONString() {
        JSONObject json = new JSONObject();
        json.put("command", commandName().toLowerCase());
        json.put("arguments", arguments);

        if (robot != null) {
            json.put("robot", robot.getName());
        }

        return json.toString();
    }

    public static Command fromJSON(JSONObject json) {
        String command = json.getString("command").toLowerCase();
        if (command.equals("disconnect")) return new DisconnectCommand(); // handle disconnect command separately

        Robot robot = createRobotFromJson(json);
        String[] args = extractArgsFromJSON(json.getJSONArray("arguments"));

        return createCommand(command,robot,args);
    }
    private static Robot createRobotFromJson(JSONObject json) {
        if (!json.has("robot")) {
            return null; // Some commands like dump don't need a robot
        }
        String robotName = json.getString("robot");
        return new Robot(robotName);
    }

    private static String[] extractArgsFromJSON(JSONArray jsonArgs) {
        String[] args = new String[jsonArgs.length()];
        for (int i = 0; i < jsonArgs.length(); i++) {
            args[i] = jsonArgs.getString(i);
        }
        return args;
    }

    private static Command createCommand(String command,Robot robot,String[] args){
        return switch (command) {
            case "repair" -> new RepairCommand(robot, args);
            case "reload" -> new ReloadCommand(robot, args);
            case "help" -> createStatelessCommand(robot, HelpCommand.class);
            case "dump" -> createStatelessCommand(robot, DumpCommand.class);
            case "look" -> createStatelessCommand(robot, LookCommand.class);
            case "state" -> createStatelessCommand(robot, StateCommand.class);
            case "launch" -> new LaunchCommand(new Robot(robot.getName(), args[0]), args);
            case "forward" -> new MoveCommand(robot, "forward", args);
            case "back" -> new MoveCommand(robot, "back", args);
            case "turn" -> new TurnCommand(robot, args);
            case "orientation" -> new OrientationCommand(robot);
            case "shutdown","off" -> createStatelessCommand(robot, ShutdownCommand.class);
            case "fire" -> new FireCommand(robot, args);
            case "world" -> createStatelessCommand(robot, WorldCommand.class);
            case "mine" -> createStatelessCommand(robot, MineCommand.class);
            default -> throw new IllegalArgumentException("Unknown command: " + command);
        };
    }

    private static Command createStatelessCommand(Robot robot, Class<? extends Command> clazz) {
        try {
            return clazz.getConstructor(Robot.class, String[].class)
                    .newInstance(robot, new String[0]);
        } catch (ReflectiveOperationException e) {
            throw new IllegalStateException("Failed to create stateless command: " + clazz.getSimpleName(), e);
        }
    }


    public static Command fromInput(String input, String robotName) {
        String[] tokens = input.trim().split(" ");
        if (tokens.length == 0 || tokens[0].isEmpty()) {
            throw new IllegalArgumentException("Invalid or empty command ");
        }
        String command = tokens[0].toLowerCase();
        String robot = resolveRobotName(command, tokens, robotName);
        String[] args = buildArguments(command, tokens, robot);

        JSONObject json = new JSONObject()
                .put("command", command)
                .put("arguments", new JSONArray(Arrays.asList(args)));

        // Only add robot field if robot is not null (some commands like dump don't need a robot)
        if (robot != null) {
            json.put("robot", robot);
        }

        return fromJSON(json);
    }
    //
    private static String resolveRobotName(String command, String[] tokens, String defaultName) {
        // Commands that don't need a robot
        if (Set.of("dump", "help", "disconnect").contains(command)) {
            return null;
        }

        // Movement commands: "forward 1" or "forward hal 1"
        if (Set.of("forward", "back").contains(command)) {
            if (tokens.length >= 3) {
                return tokens[1]; // "forward hal 1" -> robot is "hal"
            } else {
                return defaultName; // "forward 1" -> use default robot name
            }
        }

        // Turn command: "turn right" or "turn hal right"
        if ("turn".equals(command)) {
            if (tokens.length >= 3) {
                return tokens[1]; // "turn hal right" -> robot is "hal"
            } else {
                return defaultName; // "turn right" -> use default robot name
            }
        }

        // Launch command: "launch tank" or "launch tank hal"
        if ("launch".equals(command)) {
            if (tokens.length >= 3) {
                return tokens[2]; // "launch tank hal" -> robot is "hal"
            } else {
                return defaultName; // "launch tank" -> use default robot name
            }
        }

        // For other commands, check if robot name is explicitly provided
        return tokens.length > 1 ? tokens[1] : defaultName;
    }


    //
    private static String[] buildArguments(String command, String[] tokens, String robot) {
        return switch (command) {
            case "forward", "back" -> buildMoveArgs(tokens, robot);
            case "turn" ->  buildTurnArgs(tokens);
            case "launch" -> new String[]{tokens.length >= 2 ? tokens[1] : ""};
            default -> new String[0];
        };
    }

    private static String[] buildMoveArgs(String[] tokens, String robot) {
        if (tokens.length >= 3) {
            // "forward hal 1" -> [forward, hal, 1]
            return new String[]{tokens[0], tokens[1], tokens[2]};
        } else {
            // "forward 1" -> [forward, robot, 1]
            return new String[]{tokens[0], robot, tokens.length > 1 ? tokens[1] : "1"};
        }
    }

    private static String[] buildTurnArgs(String[] tokens) {
        if (tokens.length >= 3) {
            // "turn hal right" -> [right]
            return new String[]{tokens[2]};
        } else {
            // "turn right" -> [right]
            return new String[]{tokens.length > 1 ? tokens[1] : "left"};
        }
    }



    public abstract String commandName();
}




