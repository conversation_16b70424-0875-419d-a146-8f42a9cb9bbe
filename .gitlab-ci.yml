stages:
  - build
  - test

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"

cache:
  paths:
    - .m2/repository/
    - target/

build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash
  script:
    - echo "Compiling the project..."
    - make compile
  artifacts:
    paths:
      - target/
    expire_in: 1 hour

test_with_reference_server:
  stage: test
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash procps
  script:
    - echo "Starting reference server in background..."
    - java -jar "./libs/reference-server-0.1.0.jar" &
    - echo $! > server.pid
    - echo "Waiting for server to start..."
    - sleep 5
    - echo "Running tests against reference server..."
    - mvn test
  after_script:
    - echo "Stopping reference server..."
    - if [ -f server.pid ]; then kill $(cat server.pid) && rm server.pid; fi
    - echo "Server cleanup completed"
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/
    expire_in: 1 week
  dependencies:
    - build
