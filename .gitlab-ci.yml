stages:
  - build
  - test

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"

cache:
  paths:
    - .m2/repository/
    - target/

build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash
  script:
    - echo "Compiling the project..."
    - mvn clean compile test-compile
  artifacts:
    paths:
      - target/
    expire_in: 1 hour

test_with_own_server:
  stage: test
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash procps
  script:
    - echo "Starting own server in background..."
    - nohup mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.server.Server" -Dexec.args="-p 5000 -s 2" > server.log 2>&1 &
    - echo $! > own_server.pid
    - echo "Waiting for server to start..."
    - sleep 10
    - echo "Checking if server is running..."
    - if ! ps -p $(cat own_server.pid) > /dev/null; then echo "Server failed to start"; cat server.log; exit 1; fi
    - echo "Running tests against own server..."
    - mvn test -Dtest="!ServerTest"
  after_script:
    - echo "Stopping own server..."
    - if [ -f own_server.pid ]; then kill $(cat own_server.pid) && rm own_server.pid; fi
    - echo "Server cleanup completed"
    - echo "Server logs:"
    - if [ -f server.log ]; then cat server.log; fi
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/
      - server.log
    expire_in: 1 week
  dependencies:
    - build

test_server_class:
  stage: test
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash procps
  script:
    - echo "Running ServerTest (creates its own server instance)..."
    - mvn test -Dtest="ServerTest"
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/
    expire_in: 1 week
  dependencies:
    - build
