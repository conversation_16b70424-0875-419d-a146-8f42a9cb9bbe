stages:
  - build
  - test

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"

cache:
  paths:
    - .m2/repository/
    - target/

build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash
  script:
    - echo "Compiling the project..."
    - make compile
  artifacts:
    paths:
      - target/
    expire_in: 1 hour

test_with_own_server:
  stage: test
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash procps
  script:
    - echo "Starting own server in background..."
    - printf "5000\n" | mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.server.Server" &
    - echo $! > own_server.pid
    - echo "Waiting for server to start..."
    - sleep 5
    - echo "Running tests against own server..."
    - mvn test
  after_script:
    - echo "Stopping own server..."
    - if [ -f own_server.pid ]; then kill $(cat own_server.pid) && rm own_server.pid; fi
    - echo "Server cleanup completed"
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/
    expire_in: 1 week
  dependencies:
    - build
