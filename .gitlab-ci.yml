stages:
  - build
  - test

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"

cache:
  paths:
    - .m2/repository/
    - target/

build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash
  script:
    - echo "Building the project..."
    - mvn clean compile test-compile package
  artifacts:
    paths:
      - target/
    expire_in: 1 hour

test_all:
  stage: test
  image: maven:3.9.6-eclipse-temurin-21
  before_script:
    - apt-get update && apt-get install -y make bash procps
  script:
    - echo "Starting own server in background..."
    - java -jar target/robot-world-1.1.2-jar-with-dependencies.jar -p 5000 -s 2 > server.log 2>&1 &
    - echo $! > own_server.pid
    - echo "Waiting for server to start..."
    - sleep 10
    - echo "Checking if server is running..."
    - if ! ps -p $(cat own_server.pid) > /dev/null; then echo "Server failed to start"; cat server.log; exit 1; fi
    - echo "Running all tests..."
    - mvn test
  after_script:
    - echo "Stopping own server..."
    - if [ -f own_server.pid ]; then kill $(cat own_server.pid) && rm own_server.pid; fi
    - echo "Server cleanup completed"
    - echo "Server logs:"
    - if [ -f server.log ]; then cat server.log; fi
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/
      - server.log
    expire_in: 1 week
  dependencies:
    - build
