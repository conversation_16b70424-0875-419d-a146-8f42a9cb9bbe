package za.co.wethinkcode.robots.server;
import za.co.wethinkcode.flow.Recorder;
import za.co.wethinkcode.robots.handlers.ClientHandler;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;

/**
 * Main server class that accepts client connections and provides an admin console for server control.
 * Supports real-time robot monitoring, world state inspection, and graceful shutdown.
 */
@Command(name = "robot-server", mixinStandardHelpOptions = true, version = "1.0.0",
         description = "Robot World Server - Manages robot connections and world state")
public class Server implements Runnable {
    private static volatile boolean isRunning = true;
    private static ServerSocket serverSocket;

    @Option(names = {"-p", "--port"}, description = "Server port (0-9999, default: ${DEFAULT-VALUE})", defaultValue = "5000")
    private int port;

    @Option(names = {"-s", "--size"}, description = "World size (1-9999, creates size x size world, default: ${DEFAULT-VALUE})", defaultValue = "1")
    private int worldSize;

    @Option(names = {"-o", "--obstacles"}, description = "Obstacle positions in format x,y (default: none)", split = ",")
    private String[] obstaclePositions;

    public static void main(String[] args) {
        int exitCode = new CommandLine(new Server()).execute(args);
        System.exit(exitCode);
    }

    @Override
    public void run() {
        // Validate and sanitize arguments
        int validatedPort = validatePort(port);

        // Create world with validated size using World's static method
        World world = World.createWorldWithSize(worldSize);

        // Add obstacles if specified using World's method
        if (obstaclePositions != null && obstaclePositions.length >= 2) {
            world.addObstacleFromCommandLine(obstaclePositions);
        }

        try {
            serverSocket = new ServerSocket(validatedPort);
            System.out.println("Server started on port " + validatedPort + ". Waiting for clients...");

            // launch admin console thread
            startAdminConsole(world);

            while (isRunning) {
                Socket clientSocket = serverSocket.accept();
                System.out.println("New client connected: " + clientSocket.getRemoteSocketAddress());
                new Thread(new ClientHandler(clientSocket, world)).start(); // start new thread to handle multiple clients
            }

        } catch (IOException e) {
            if (!isRunning) {
                System.out.println("Sever shutdown.");
            } else {
                System.out.println("Got an error: " + e);
            }
        }
    }

    /**
     * Validates port number is within range 0-9999, returns default if invalid
     */
    private int validatePort(int port) {
        if (port < 0 || port > 9999) {
            System.out.println("Warning: Port " + port + " is out of range (0-9999). Using default port 5000.");
            return 5000;
        }
        return port;
    }


    private static void startAdminConsole(World world) {
        new Thread(() -> {
            try (Scanner scanner = new Scanner(System.in)) {
                while (isRunning) {
                    System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'display'");
                    System.out.print("[Admin]: ");
                    String input = scanner.nextLine().trim().toLowerCase();
                    switch (input) {
                        case "quit":
                            System.out.println("Shutting down server...");
                            shutdown();
                            break;
                        case "robots":
                            System.out.println(world.getAllRobotsInfo());
                            break;
                        case "dump":
                            System.out.println(world.getFullWorldState());
                            break;
                        case "display":
                            world.displayWorld();
                            break;
                        default:
                            System.out.println("Unknown admin command.");
                    }
                }
            }
        }, "AdminConsole").start();
    }


    public static void shutdown() {
        isRunning = false;
        try {
            serverSocket.close();
        } catch (IOException e) {
            System.out.println("Got an error when shutting down: " + e);
        }
    }

    static {
        try {
            new Recorder().logRun();
        } catch (Exception e) {
            System.out.println("Git Recorder disabled: " + e.getMessage());
        }
    }
}

