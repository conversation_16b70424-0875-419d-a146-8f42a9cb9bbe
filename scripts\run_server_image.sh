#!/bin/bash

# Get the local IP address (for LAN access)
IP=$(hostname -I | awk '{print $1}')

# Run Docker container
echo "🚀 Starting Robot Worlds Server..."
docker run -it -p 5050:5050 --name robot-worlds robot-worlds-server:1.0.0 .

# Show connection info
echo "✅ Server is running!"
echo "👉 Connect using: ${IP}:5050"
echo "🔁 To view logs: docker logs -f robot-worlds"
echo "🛑 To stop the container: docker stop robot-worlds && docker rm robot-worlds"
