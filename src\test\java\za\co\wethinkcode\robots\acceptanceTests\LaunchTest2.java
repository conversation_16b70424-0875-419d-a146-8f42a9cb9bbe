package za.co.wethinkcode.robots.acceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.server.RobotWorldClient;
import za.co.wethinkcode.robots.server.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.World;

import static org.junit.jupiter.api.Assertions.*;

public class LaunchTest2 {
    private final static int DEFAULT_PORT = 5001;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private static Thread serverThread;

    @BeforeAll
    static void startServer() throws Exception {
        serverThread = new Thread(() -> {
            try {
                za.co.wethinkcode.robots.server.Server.main(new String[]{"-p", String.valueOf(DEFAULT_PORT), "-s", "2"});
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        serverThread.setDaemon(true);  // Make it a daemon thread so <PERSON>V<PERSON> can exit
        serverThread.start();

        // Give server a moment to start
        Thread.sleep(2000);
    }

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    void canLaunchAnotherRobot() {

        assertTrue(serverClient.isConnected());

        //Given a world of size 2x2
        //And robot "blarn" has already been launched into the world
        String launchRequest = "{" +
                "  \"robot\": \"blarn\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"tank\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);
        System.out.println("Launch Response:\n" + launchResponse.toPrettyString());

        assertEquals("OK", launchResponse.get("result").asText());

        //When I launch robot "bojack" into the world
        String launchRequest2 = "{" +
                "  \"robot\": \"bojack\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"tank\"]" +
                "}";
        JsonNode launchResponse2 = serverClient.sendRequest(launchRequest2);
        System.out.println("Launch Response:\n" + launchResponse2.toPrettyString());

        //Then the launch should be successful
        assertEquals("OK", launchResponse2.get("result").asText());

        //And a randomly allocated position of bojack should be included in the message.
        String expectedMessage = "Launched bojack into the world at ";
        String actualMessage = launchResponse2.get("message").asText();
        assertTrue(actualMessage.contains(expectedMessage));
    }

    @Test
    void worldWithoutObstaclesIsFull() {
        assertTrue(serverClient.isConnected());


    }

}
