#!/bin/bash
set -e

COMMAND=$1

CURRENT_VERSION=$(grep '<version>' pom.xml | head -1 | sed -E 's/.*<version>([^<]+)<\/version>.*/\1/')
BASE_VERSION=${CURRENT_VERSION%-SNAPSHOT}
IFS='.' read -r MAJOR MINOR PATCH <<< "$BASE_VERSION"

case $COMMAND in
  PATCH)
    PATCH=$((PATCH + 1))
    ;;
  MAJOR)
    MAJOR=$((MAJOR + 1))
    MINOR=0
    PATCH=0
    ;;
  MINOR)
    MINOR=$((MINOR + 1))
    PATCH=0
    ;;
  "")
    ITERATION=$(grep -oE '\[.*iteration-([0-9]+)\]' .lms/exercises.toml | grep -oE '[0-9]+')
     if [[ -z "$ITERATION" ]]; then
       echo "❌ Could not find iteration number in .lms/exercises.toml"
       exit 1
     fi

     MAJOR=$((ITERATION - 1))
     if (( MAJOR < 0 )); then
       MAJOR=0
     fi
     MINOR=0
     PATCH=0
     ;;
  *)
    echo "Unknown command $COMMAND"
    exit 1
    ;;
esac

NEW_VERSION="${MAJOR}.${MINOR}.${PATCH}-SNAPSHOT"

# Update pom.xml with new version
sed -i  "0,/<version>.*<\/version>/s//<version>${NEW_VERSION}<\/version>/" pom.xml

echo "Bumped version to $NEW_VERSION"
